/**
 * Test script to verify ExtendScript functions return proper JSON
 * This can be run in Adobe ExtendScript Toolkit to test the fixes
 */

// Include the main ExtendScript file
#include "host/ae-integration.jsxinc"

// Test function to verify JSON responses
function testExtendScriptFunctions() {
    var results = [];
    
    try {
        // Test SahAI.getAppInfo()
        var appInfoResult = SahAI.getAppInfo();
        results.push("SahAI.getAppInfo(): " + appInfoResult);
        
        // Verify it's valid JSON
        try {
            JSON.parse(appInfoResult);
            results.push("✓ SahAI.getAppInfo() returns valid JSON");
        } catch (e) {
            results.push("✗ SahAI.getAppInfo() returns invalid JSON: " + e.toString());
        }
        
        // Test SahAI.getSystemInfo()
        var systemInfoResult = SahAI.getSystemInfo();
        results.push("SahAI.getSystemInfo(): " + systemInfoResult);
        
        // Verify it's valid JSON
        try {
            JSON.parse(systemInfoResult);
            results.push("✓ SahAI.getSystemInfo() returns valid JSON");
        } catch (e) {
            results.push("✗ SahAI.getSystemInfo() returns invalid JSON: " + e.toString());
        }
        
        // Test loadSettings()
        var settingsResult = loadSettings();
        results.push("loadSettings(): " + settingsResult);
        
        // Verify it's valid JSON
        try {
            JSON.parse(settingsResult);
            results.push("✓ loadSettings() returns valid JSON");
        } catch (e) {
            results.push("✗ loadSettings() returns invalid JSON: " + e.toString());
        }
        
        // Test SahAI.log()
        var logResult = SahAI.log("Test message", "info");
        results.push("SahAI.log(): " + logResult);
        
        // Verify it's valid JSON
        try {
            JSON.parse(logResult);
            results.push("✓ SahAI.log() returns valid JSON");
        } catch (e) {
            results.push("✗ SahAI.log() returns invalid JSON: " + e.toString());
        }
        
    } catch (error) {
        results.push("Error during testing: " + error.toString());
    }
    
    // Output all results
    for (var i = 0; i < results.length; i++) {
        $.writeln(results[i]);
    }
    
    return results.join("\n");
}

// Run the test
$.writeln("=== Testing ExtendScript Functions ===");
testExtendScriptFunctions();
$.writeln("=== Test Complete ===");
