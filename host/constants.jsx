/**
 * Constants for SahAI CEP Extension - ExtendScript Side
 * This file contains shared constants between the CEP panel and ExtendScript
 */

// File paths
var SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
var HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
var EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";

// API defaults
var DEFAULT_OLLAMA_URL = "http://localhost:11434";
var DEFAULT_LMSTUDIO_URL = "http://localhost:1234";

// HTTP settings
var DEFAULT_TIMEOUT = 10000;
var DEFAULT_RETRIES = 2;
var LOCAL_TIMEOUT = 8000;
var LOCAL_RETRIES = 1;
var API_TIMEOUT = 12000;
var API_RETRIES = 2;

// Port numbers
var OLLAMA_DEFAULT_PORT = 11434;
var LMSTUDIO_DEFAULT_PORT = 1234;
var HTTPS_DEFAULT_PORT = 443;

// Context lengths (common defaults)
var DEFAULT_CONTEXT_LENGTH = 4096;
var SMALL_CONTEXT_LENGTH = 8192;
var MEDIUM_CONTEXT_LENGTH = 32000;
var LARGE_CONTEXT_LENGTH = 128000;
var EXTRA_LARGE_CONTEXT_LENGTH = 200000;

// Note: ExtendScript doesn't support CommonJS modules
// Constants are available globally when this file is included
