/**
 * Constants for SahAI CEP Extension - ExtendScript Side
 * This file contains shared constants between the CEP panel and ExtendScript
 */

// File paths
var SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
var HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
var EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";

// API defaults
var DEFAULT_OLLAMA_URL = "http://localhost:11434";
var DEFAULT_LMSTUDIO_URL = "http://localhost:1234";

// HTTP settings
var DEFAULT_TIMEOUT = 10000;
var DEFAULT_RETRIES = 2;
var LOCAL_TIMEOUT = 8000;
var LOCAL_RETRIES = 1;
var API_TIMEOUT = 12000;
var API_RETRIES = 2;

// Port numbers
var OLLAMA_DEFAULT_PORT = 11434;
var LMSTUDIO_DEFAULT_PORT = 1234;
var HTTPS_DEFAULT_PORT = 443;

// Context lengths (common defaults)
var DEFAULT_CONTEXT_LENGTH = 4096;
var SMALL_CONTEXT_LENGTH = 8192;
var MEDIUM_CONTEXT_LENGTH = 32000;
var LARGE_CONTEXT_LENGTH = 128000;
var EXTRA_LARGE_CONTEXT_LENGTH = 200000;

// Export constants
module.exports = {
  SETTINGS_FILE_PATH: SETTINGS_FILE_PATH,
  HISTORY_FILE_PATH: HISTORY_FILE_PATH,
  EXTENSION_DIR_PATH: EXTENSION_DIR_PATH,
  DEFAULT_OLLAMA_URL: DEFAULT_OLLAMA_URL,
  DEFAULT_LMSTUDIO_URL: DEFAULT_LMSTUDIO_URL,
  DEFAULT_TIMEOUT: DEFAULT_TIMEOUT,
  DEFAULT_RETRIES: DEFAULT_RETRIES,
  LOCAL_TIMEOUT: LOCAL_TIMEOUT,
  LOCAL_RETRIES: LOCAL_RETRIES,
  API_TIMEOUT: API_TIMEOUT,
  API_RETRIES: API_RETRIES,
  OLLAMA_DEFAULT_PORT: OLLAMA_DEFAULT_PORT,
  LMSTUDIO_DEFAULT_PORT: LMSTUDIO_DEFAULT_PORT,
  HTTPS_DEFAULT_PORT: HTTPS_DEFAULT_PORT,
  DEFAULT_CONTEXT_LENGTH: DEFAULT_CONTEXT_LENGTH,
  SMALL_CONTEXT_LENGTH: SMALL_CONTEXT_LENGTH,
  MEDIUM_CONTEXT_LENGTH: MEDIUM_CONTEXT_LENGTH,
  LARGE_CONTEXT_LENGTH: LARGE_CONTEXT_LENGTH,
  EXTRA_LARGE_CONTEXT_LENGTH: EXTRA_LARGE_CONTEXT_LENGTH
};
