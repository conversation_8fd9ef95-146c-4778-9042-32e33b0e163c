import React from 'react';
import { BaseProviderComponent, PROVIDER_CONFIGS } from '../components/ui/BaseProviderComponent';

interface Props {
  onClose: () => void;
}

export const OllamaProvider: React.FC<Props> = ({ onClose }) => {
  const handleSave = (config: { apiKey?: string; baseURL?: string; selectedModelId: string }) => {
    // For Ollama, we use baseURL instead of apiKey
    // You might need to update your store to handle this properly
    // For now, we'll pass the baseURL as the apiKey parameter to maintain compatibility
    onClose();
  };

  return (
    <BaseProviderComponent
      config={PROVIDER_CONFIGS.ollama}
      onSave={handleSave}
    />
  );
};
