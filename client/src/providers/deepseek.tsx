import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { SearchableModelSelect } from '../components/ui/SearchableModelSelect';

export const DeepSeekProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [selectedModel, setSelectedModel] = useState('');

  useEffect(() => {
    if (key) useSettingsStore.getState().loadModelsForProvider('deepseek');
  }, [key]);

  const models = useSettingsStore(state => state.providers.find(p => p.id === 'deepseek')?.models || []);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <SearchableModelSelect
          models={models}
          value={selectedModel}
          onChange={handleModelChange}
          placeholder="Search DeepSeek models..."
        />
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="sk-..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <button
        onClick={() => { updateProviderKey('deepseek', key, selectedModel); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
