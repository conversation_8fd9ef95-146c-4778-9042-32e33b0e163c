import React from 'react';
import { BaseProviderComponent, PROVIDER_CONFIGS } from '../components/ui/BaseProviderComponent';

interface Props {
  onSave: (config: { apiKey: string; selectedModelId: string }) => void;
}

export const OpenAIProvider: React.FC<Props> = ({ onSave }) => {
  const handleSave = (config: { apiKey?: string; baseURL?: string; selectedModelId: string }) => {
    onSave({
      apiKey: config.apiKey!,
      selectedModelId: config.selectedModelId
    });
  };

  return (
    <BaseProviderComponent
      config={PROVIDER_CONFIGS.openai}
      onSave={handleSave}
    />
  );
};
