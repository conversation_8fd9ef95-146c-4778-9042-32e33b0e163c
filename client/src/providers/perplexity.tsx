import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const PerplexityProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProv<PERSON><PERSON>ey } = useSettingsStore();
  const [key, setKey] = useState('');

  useEffect(() => {
    if (key) useSettingsStore.getState().loadModelsForProvider('perplexity');
  }, [key]);

  const models = useSettingsStore(state => state.providers.find(p => p.id === 'perplexity')?.models || []);

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('perplexity', key, e.target.value)}
        >
          <option value="">Select a model...</option>
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="pplx-..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <button
        onClick={() => { updateProviderKey('perplexity', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
