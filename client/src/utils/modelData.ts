/**
 * Shared model data for all providers
 * This file serves as the single source of truth for model information
 * Used by both client and host scripts
 */

import { 
  DEFAULT_CONTEXT_LENGTH, 
  SMALL_CONTEXT_LENGTH, 
  MEDIUM_CONTEXT_LENGTH, 
  LARGE_CONTEXT_LENGTH, 
  EXTRA_LARGE_CONTEXT_LENGTH 
} from './constants';

export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  isRecommended: boolean;
}

export interface ProviderModelData {
  [providerId: string]: ModelInfo[];
}

export const FALLBACK_MODELS: ProviderModelData = {
  openai: [
    { 
      id: 'gpt-4o', 
      name: 'GPT-4o', 
      description: 'Most capable OpenAI model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'gpt-4o-mini', 
      name: 'GPT-4o Mini', 
      description: 'Faster, more affordable', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'gpt-4-turbo', 
      name: 'GPT-4 Turbo', 
      description: 'Previous generation flagship', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'gpt-3.5-turbo', 
      name: 'GPT-3.5 Turbo', 
      description: 'Legacy model', 
      contextLength: 16384, 
      isRecommended: false 
    }
  ],
  
  anthropic: [
    { 
      id: 'claude-3-5-sonnet-20241022', 
      name: 'Claude 3.5 Sonnet', 
      description: 'Anthropic\'s most capable model', 
      contextLength: EXTRA_LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'claude-3-5-haiku-20241022', 
      name: 'Claude 3.5 Haiku', 
      description: 'Fast and efficient', 
      contextLength: EXTRA_LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'claude-3-opus-20240229', 
      name: 'Claude 3 Opus', 
      description: 'Powerful reasoning', 
      contextLength: EXTRA_LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'claude-2.1', 
      name: 'Claude 2.1', 
      description: 'Previous generation', 
      contextLength: EXTRA_LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  gemini: [
    { 
      id: 'gemini-1.5-pro', 
      name: 'Gemini 1.5 Pro', 
      description: 'Google\'s most capable model', 
      contextLength: 2000000, 
      isRecommended: true 
    },
    { 
      id: 'gemini-1.5-flash', 
      name: 'Gemini 1.5 Flash', 
      description: 'Fast and efficient', 
      contextLength: 1000000, 
      isRecommended: false 
    },
    { 
      id: 'gemini-1.0-pro', 
      name: 'Gemini 1.0 Pro', 
      description: 'Previous generation', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  groq: [
    { 
      id: 'llama-3.1-70b-versatile', 
      name: 'Llama 3.1 70B', 
      description: 'Balanced performance', 
      contextLength: 131072, 
      isRecommended: true 
    },
    { 
      id: 'llama-3.1-8b-instant', 
      name: 'Llama 3.1 8B', 
      description: 'Fast inference', 
      contextLength: 131072, 
      isRecommended: false 
    },
    { 
      id: 'mixtral-8x7b-32768', 
      name: 'Mixtral 8x7B', 
      description: 'Large context', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  deepseek: [
    { 
      id: 'deepseek-chat', 
      name: 'DeepSeek Chat', 
      description: 'General purpose', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'deepseek-coder', 
      name: 'DeepSeek Coder', 
      description: 'Code-focused', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  mistral: [
    { 
      id: 'mistral-large-latest', 
      name: 'Mistral Large', 
      description: 'Most capable', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'mistral-medium-latest', 
      name: 'Mistral Medium', 
      description: 'Balanced', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'mistral-small-latest', 
      name: 'Mistral Small', 
      description: 'Fast and efficient', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  moonshot: [
    { 
      id: 'moonshot-v1-128k', 
      name: 'Moonshot v1 128K', 
      description: 'Large context', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'moonshot-v1-32k', 
      name: 'Moonshot v1 32K', 
      description: 'Medium context', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'moonshot-v1-8k', 
      name: 'Moonshot v1 8K', 
      description: 'Small context', 
      contextLength: SMALL_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  openrouter: [
    { 
      id: 'openai/gpt-4o', 
      name: 'GPT-4o (OpenRouter)', 
      description: 'OpenAI via OpenRouter', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'anthropic/claude-3.5-sonnet', 
      name: 'Claude 3.5 Sonnet (OpenRouter)', 
      description: 'Anthropic via OpenRouter', 
      contextLength: EXTRA_LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'meta-llama/llama-3.1-70b-instruct', 
      name: 'Llama 3.1 70B (OpenRouter)', 
      description: 'Meta via OpenRouter', 
      contextLength: 131072, 
      isRecommended: false 
    }
  ],
  
  perplexity: [
    { 
      id: 'llama-3.1-sonar-large-128k-online', 
      name: 'Llama 3.1 Sonar Large 128K Online', 
      description: 'Large online model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'llama-3.1-sonar-small-128k-online', 
      name: 'Llama 3.1 Sonar Small 128K Online', 
      description: 'Small online model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'llama-3.1-sonar-huge-128k-online', 
      name: 'Llama 3.1 Sonar Huge 128K Online', 
      description: 'Huge online model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  qwen: [
    { 
      id: 'qwen-max', 
      name: 'Qwen Max', 
      description: 'Most capable', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'qwen-plus', 
      name: 'Qwen Plus', 
      description: 'Balanced performance', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'qwen-turbo', 
      name: 'Qwen Turbo', 
      description: 'Fast and efficient', 
      contextLength: SMALL_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  together: [
    { 
      id: 'meta-llama/Llama-3-70b-chat-hf', 
      name: 'Llama 3 70B Chat', 
      description: 'Large language model', 
      contextLength: SMALL_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'meta-llama/Llama-3-8b-chat-hf', 
      name: 'Llama 3 8B Chat', 
      description: 'Smaller, faster model', 
      contextLength: SMALL_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', 
      name: 'Mixtral 8x7B Instruct', 
      description: 'Mixture of experts', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  vertex: [
    { 
      id: 'gemini-1.5-pro', 
      name: 'Gemini 1.5 Pro', 
      description: 'Google\'s most capable model', 
      contextLength: 2000000, 
      isRecommended: true 
    },
    { 
      id: 'gemini-1.5-flash', 
      name: 'Gemini 1.5 Flash', 
      description: 'Fast and efficient', 
      contextLength: 1000000, 
      isRecommended: false 
    },
    { 
      id: 'gemini-1.0-pro', 
      name: 'Gemini 1.0 Pro', 
      description: 'Previous generation', 
      contextLength: MEDIUM_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  xai: [
    { 
      id: 'grok-beta', 
      name: 'Grok Beta', 
      description: 'xAI\'s flagship model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'grok-vision-beta', 
      name: 'Grok Vision Beta', 
      description: 'Vision-capable model', 
      contextLength: LARGE_CONTEXT_LENGTH, 
      isRecommended: false 
    }
  ],
  
  ollama: [
    { 
      id: 'llama3.1', 
      name: 'Llama 3.1', 
      description: 'Open source LLM', 
      contextLength: DEFAULT_CONTEXT_LENGTH, 
      isRecommended: true 
    },
    { 
      id: 'mistral', 
      name: 'Mistral', 
      description: 'Efficient transformer', 
      contextLength: SMALL_CONTEXT_LENGTH, 
      isRecommended: false 
    },
    { 
      id: 'codellama', 
      name: 'Code Llama', 
      description: 'Code-focused', 
      contextLength: 16384, 
      isRecommended: false 
    }
  ],
  
  lmstudio: [
    { 
      id: 'local-model', 
      name: 'Local Model', 
      description: 'Your local model', 
      contextLength: DEFAULT_CONTEXT_LENGTH, 
      isRecommended: true 
    }
  ]
};

/**
 * Get fallback models for a specific provider
 */
export function getFallbackModels(providerId: string): ModelInfo[] {
  return FALLBACK_MODELS[providerId] || [];
}

/**
 * Get model description by provider and model ID
 */
export function getModelDescription(providerId: string, modelId: string): string {
  const models = FALLBACK_MODELS[providerId] || [];
  const model = models.find(m => m.id === modelId);
  return model?.description || '';
}

/**
 * Get model context length by provider and model ID
 */
export function getModelContextLength(providerId: string, modelId: string): number {
  const models = FALLBACK_MODELS[providerId] || [];
  const model = models.find(m => m.id === modelId);
  return model?.contextLength || DEFAULT_CONTEXT_LENGTH;
}

/**
 * Check if a model is recommended for a provider
 */
export function isModelRecommended(providerId: string, modelId: string): boolean {
  const models = FALLBACK_MODELS[providerId] || [];
  const model = models.find(m => m.id === modelId);
  return model?.isRecommended || false;
}
