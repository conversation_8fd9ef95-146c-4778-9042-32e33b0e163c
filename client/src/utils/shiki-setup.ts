// Optimized Shiki setup for CEP extension
// Uses dynamic imports to load only essential languages on demand

import { createHighlighter } from 'shiki';
import type { Highlighter, BundledLanguage, BundledTheme } from 'shiki';

// Define only the most essential languages for Adobe CEP workflows
// Reduced from 17 to 8 languages to optimize bundle size
const ESSENTIAL_LANGUAGES: BundledLanguage[] = [
  // Core Web/CEP Technologies (most common)
  'javascript',
  'typescript',
  'jsx',
  'tsx',

  // Configuration Files (essential for CEP)
  'json',
  'xml',

  // Documentation and Shell
  'markdown',
  'shell'
];

const ESSENTIAL_THEMES: BundledTheme[] = ['github-dark'];

/**
 * A promise that resolves to a configured Shiki highlighter.
 * Uses only essential languages for Adobe CEP workflows to reduce bundle size.
 */
export const highlighterPromise = createHighlighter({
  themes: ESSENTIAL_THEMES,
  langs: ESSENTIAL_LANGUAGES
});

/**
 * Helper to get the highlighter in async/await style:
 * 
 * Usage:
 *   import { highlighterPromise } from './utils/shiki-setup'
 *   const highlighter = await highlighterPromise
 *   const html = highlighter.codeToHtml(code, { lang: 'typescript' })
 */

/**
 * Cached highlighter instance to avoid repeated initialization
 */
let cachedHighlighter: Highlighter | null = null;

/**
 * Get the highlighter instance (cached after first load)
 */
export async function getOptimizedHighlighter(): Promise<Highlighter> {
  if (!cachedHighlighter) {
    cachedHighlighter = await highlighterPromise;
  }
  return cachedHighlighter;
}

/**
 * Check if a language is supported by our optimized setup
 */
export function isSupportedLanguage(lang: string): boolean {
  const supportedLangs = [
    'javascript', 'typescript', 'jsx', 'tsx',
    'json', 'xml', 'markdown', 'shell'
  ];

  return supportedLangs.includes(lang.toLowerCase());
}

/**
 * Get fallback language for unsupported languages
 */
export function getFallbackLanguage(lang: string): string {
  // Map common aliases to supported languages (only essential ones)
  const aliasMap: Record<string, string> = {
    'js': 'javascript',
    'ts': 'typescript',
    'bash': 'shell',
    'sh': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    // Fallback unsupported languages to closest supported ones
    'py': 'javascript',
    'rb': 'javascript',
    'yml': 'json',
    'yaml': 'json',
    'htm': 'json',
    'html': 'json',
    'css': 'json',
    'sass': 'json',
    'scss': 'json'
  };
  
  const normalizedLang = lang.toLowerCase();
  
  // Return mapped language if available
  if (aliasMap[normalizedLang]) {
    return aliasMap[normalizedLang];
  }
  
  // Return original if supported
  if (isSupportedLanguage(normalizedLang)) {
    return normalizedLang;
  }
  
  // Default fallback to javascript for unknown languages
  return 'javascript';
}
