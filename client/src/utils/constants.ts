// Core timeout and retry constants
export const DEFAULT_TIMEOUT = 30000;
export const DEFAULT_RETRIES = 2;
export const DEFAULT_MODEL_TIMEOUT = 10000;
export const DEFAULT_MODEL_RETRIES = 3;
// MAX_RETRY_DELAY removed - not used in current implementation
export const BASE_RETRY_DELAY = 1000;

// Provider-specific timeouts - currently unused but kept for future use
export const LOCAL_PROVIDER_TIMEOUT = 8000;
export const API_PROVIDER_TIMEOUT = 12000;

// Debounce delays
export const API_KEY_DEBOUNCE_DELAY = 500;
export const BASE_URL_DEBOUNCE_DELAY = 500;

// Toast durations
export const DEFAULT_TOAST_DURATION = 4000;
export const SUCCESS_TOAST_DURATION = 3000;
export const ERROR_TOAST_DURATION = 5000;

// Port numbers
export const OLLAMA_DEFAULT_PORT = 11434;
export const LMSTUDIO_DEFAULT_PORT = 1234;
export const HTTPS_DEFAULT_PORT = 443;

// Context lengths (common defaults)
export const DEFAULT_CONTEXT_LENGTH = 4096;
export const SMALL_CONTEXT_LENGTH = 8192;
export const MEDIUM_CONTEXT_LENGTH = 32000;
export const LARGE_CONTEXT_LENGTH = 128000;
export const EXTRA_LARGE_CONTEXT_LENGTH = 200000;

// API endpoints - moved to BaseProviderComponent configuration

// Local provider defaults
export const OLLAMA_DEFAULT_URL = 'http://localhost:11434';
export const LMSTUDIO_DEFAULT_URL = 'http://localhost:1234';

// Storage keys
export const SETTINGS_STORAGE_KEY = 'sahAI_settings';
export const HISTORY_STORAGE_KEY = 'sahai-chat-history';

// File paths
export const SETTINGS_FILE_PATH = '~/Adobe/CEP/extensions/SahAI/settings.json';
export const HISTORY_FILE_PATH = '~/Adobe/CEP/extensions/SahAI/history.json';
