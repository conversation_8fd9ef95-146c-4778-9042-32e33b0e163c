import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useChatStore } from '../stores/chatStore';
import { Paperclip, Send, Mic, Loader2 } from 'lucide-react';

// Removed unused interface properties - InputArea doesn't use any props currently

const InputArea: React.FC = React.memo(() => {
  const [input, setInput] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { addMessage, isLoading, setLoading, currentSession, createNewSession } = useChatStore();

  const maxChars = 4000;
  const isEmpty = !input.trim();
  const isNearLimit = input.length > maxChars * 0.9;

  // Set initial height and handle resizing
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.setProperty('--textarea-height', '72px');
    }
  }, []);

  const resizeTextarea = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Temporarily set to auto to get scroll height
    textarea.style.height = 'auto';
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 72), 200);
    textarea.style.setProperty('--textarea-height', `${newHeight}px`);
    textarea.style.height = ''; // Reset to use CSS custom property
  }, []);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    resizeTextarea();
  }, [resizeTextarea]);

  const handleSend = useCallback(async () => {
    const text = input.trim();
    if (!text || isLoading) return;

    setInput('');
    if (textareaRef.current) {
      textareaRef.current.style.setProperty('--textarea-height', '72px');
    }

    try {
      setLoading(true);
      if (!currentSession) createNewSession();
      addMessage({ content: text, role: 'user' });

      setTimeout(() => {
        addMessage({ content: `Echo: ${text}`, role: 'assistant' });
        setLoading(false);
      }, 1000);
    } catch (err) {
      setInput(text);
      setLoading(false);
    }
  }, [input, isLoading, currentSession, addMessage, setLoading, createNewSession]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend, isComposing]
  );

  return (
    <div className="px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border">
      <div className="relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors">
        <div className="flex items-center pl-3">
          <button
            onClick={onAttachFile}
            className="text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded"
            title="Attach file"
            disabled={isLoading}
          >
            <Paperclip size={18} />
          </button>
        </div>

        <textarea
          ref={textareaRef}
          rows={3}
          maxLength={maxChars}
          value={input}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          placeholder="Type a message..."
          className="flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            auto-resize-textarea leading-relaxed overflow-y-auto chat-messages-scrollbar"
        />

        <div className="flex items-center pr-3 space-x-1">
          <button
            onClick={onVoiceInput}
            className="text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40"
            title="Voice input"
            disabled={isLoading}
          >
            <Mic size={18} />
          </button>
          <button
            onClick={handleSend}
            disabled={isEmpty || isLoading}
            className="text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50"
            title="Send"
          >
            {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
          </button>
        </div>
      </div>

      <div className="flex justify-between items-center mt-1 px-1">
        <span className={`text-xs ${isNearLimit ? 'text-adobe-warning' : 'text-adobe-text-secondary'}`}>
          {input.length}/{maxChars}
        </span>
        <span className="text-xs text-adobe-text-secondary">
          Enter to send, Shift+Enter for new line
        </span>
      </div>
    </div>
  );
});

export default InputArea;
