import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { SearchableModelSelect } from './SearchableModelSelect';
import { ProviderBridge } from '../../utils/cepIntegration';
import { Loader2 } from 'lucide-react';
import { processError, formatErrorForLogging } from '../../utils/errorHandling';

export interface ProviderConfig {
  providerId: string;
  name: string;
  configType: 'apiKey' | 'baseURL';
  apiEndpoint?: string;
  placeholder: string;
  requiresApiKey: boolean;
  defaultUrl?: string;
}

interface Props {
  config: ProviderConfig;
  onSave: (config: { apiKey?: string; baseURL?: string; selectedModelId: string }) => void;
}

export const BaseProviderComponent: React.FC<Props> = ({ config, onSave }) => {
  const { getActiveProvider } = useSettingsStore();
  const activeProvider = getActiveProvider();
  
  // State management
  const [apiKey, setApiKey] = useState(
    config.configType === 'apiKey' ? (activeProvider?.apiKey || '') : ''
  );
  const [baseURL, setBaseURL] = useState(
    config.configType === 'baseURL' ? (activeProvider?.baseURL || config.defaultUrl || '') : ''
  );
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState(activeProvider?.selectedModelId || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Load models when credentials change
  useEffect(() => {
    const credential = config.configType === 'apiKey' ? apiKey : baseURL;
    if (!credential) return;

    setLoading(true);
    setError('');
    
    const endpoint = config.configType === 'apiKey' ? config.apiEndpoint : baseURL;
    
    ProviderBridge.listModels(config.providerId, endpoint, credential)
      .then((models: any) => {
        setModels(models as { id: string; name: string }[]);
        setError('');
      })
      .catch((e: any) => {
        const errorInfo = processError(e);
        console.error(formatErrorForLogging(e, `BaseProviderComponent:${config.providerId}`));
        setError(errorInfo.userMessage);
      })
      .finally(() => setLoading(false));
  }, [apiKey, baseURL, config.providerId, config.apiEndpoint, config.configType]);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  const handleSave = () => {
    const saveConfig: { apiKey?: string; baseURL?: string; selectedModelId: string } = {
      selectedModelId: selectedModel
    };
    
    if (config.configType === 'apiKey') {
      saveConfig.apiKey = apiKey;
    } else {
      saveConfig.baseURL = baseURL;
    }
    
    onSave(saveConfig);
  };

  const renderCredentialInput = () => {
    if (config.configType === 'apiKey') {
      return (
        <div>
          <label className="block text-sm font-medium text-adobe-text-primary mb-2">
            API Key
          </label>
          <input
            type="password"
            placeholder={config.placeholder}
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
          />
        </div>
      );
    } else {
      return (
        <div>
          <label className="block text-sm font-medium text-adobe-text-primary mb-2">
            Base URL
          </label>
          <input
            type="text"
            placeholder={config.placeholder}
            value={baseURL}
            onChange={(e) => setBaseURL(e.target.value)}
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
          />
        </div>
      );
    }
  };

  const renderRetryButton = () => (
    <button
      onClick={() => {
        const credential = config.configType === 'apiKey' ? apiKey : baseURL;
        if (!credential) return;
        
        setLoading(true);
        setError('');
        
        const endpoint = config.configType === 'apiKey' ? config.apiEndpoint : baseURL;
        
        ProviderBridge.listModels(config.providerId, endpoint, credential)
          .then((models: any) => {
            setModels(models as { id: string; name: string }[]);
            setError('');
          })
          .catch((e: any) => {
            const errorInfo = processError(e);
            console.error(formatErrorForLogging(e, `BaseProviderComponent:retry:${config.providerId}`));
            setError(errorInfo.userMessage);
          })
          .finally(() => setLoading(false));
      }}
      className="text-adobe-accent text-sm hover:underline"
    >
      Retry
    </button>
  );

  const renderModelSection = () => {
    if (loading) {
      return (
        <div className="flex items-center space-x-2 text-adobe-text-secondary">
          <Loader2 size={16} className="animate-spin" />
          <span>Loading models...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="space-y-2">
          <p className="text-adobe-error text-sm">{error}</p>
          {renderRetryButton()}
        </div>
      );
    }

    if (models.length === 0) {
      return (
        <div className="space-y-2">
          <p className="text-adobe-warning text-sm">No models found. Check config or retry.</p>
          <p className="text-adobe-text-secondary text-xs">
            Verify your {config.configType === 'apiKey' ? 'API key has access to' : 'URL is correct for'} {config.name} models.
          </p>
          {renderRetryButton()}
        </div>
      );
    }

    return (
      <SearchableModelSelect
        models={models}
        value={selectedModel}
        onChange={handleModelChange}
        placeholder={`Search ${config.name} models...`}
      />
    );
  };

  return (
    <div className="space-y-4">
      {/* Credential Input Section */}
      {renderCredentialInput()}

      {/* Model Selection */}
      <div>
        <label className="block text-sm font-medium text-adobe-text-primary mb-2">
          Model
        </label>
        {renderModelSection()}
      </div>

      {/* Save Button */}
      <button
        onClick={handleSave}
        disabled={!selectedModel || (config.configType === 'apiKey' ? !apiKey : !baseURL)}
        className="w-full bg-adobe-accent text-white rounded-md py-3 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
      >
        Save Configuration
      </button>
    </div>
  );
};

// Provider configurations
export const PROVIDER_CONFIGS: Record<string, ProviderConfig> = {
  openai: {
    providerId: 'openai',
    name: 'OpenAI',
    configType: 'apiKey',
    apiEndpoint: 'https://api.openai.com/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  anthropic: {
    providerId: 'anthropic',
    name: 'Anthropic',
    configType: 'apiKey',
    placeholder: 'sk-ant-...',
    requiresApiKey: true
  },
  gemini: {
    providerId: 'gemini',
    name: 'Google Gemini',
    configType: 'apiKey',
    apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta',
    placeholder: 'AIza...',
    requiresApiKey: true
  },
  groq: {
    providerId: 'groq',
    name: 'Groq',
    configType: 'apiKey',
    apiEndpoint: 'https://api.groq.com/openai/v1',
    placeholder: 'gsk_...',
    requiresApiKey: true
  },
  deepseek: {
    providerId: 'deepseek',
    name: 'DeepSeek',
    configType: 'apiKey',
    apiEndpoint: 'https://api.deepseek.com/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  mistral: {
    providerId: 'mistral',
    name: 'Mistral',
    configType: 'apiKey',
    apiEndpoint: 'https://api.mistral.ai/v1',
    placeholder: 'api_key...',
    requiresApiKey: true
  },
  moonshot: {
    providerId: 'moonshot',
    name: 'Moonshot AI',
    configType: 'apiKey',
    apiEndpoint: 'https://api.moonshot.cn/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  openrouter: {
    providerId: 'openrouter',
    name: 'OpenRouter',
    configType: 'apiKey',
    apiEndpoint: 'https://openrouter.ai/api/v1',
    placeholder: 'sk-or-...',
    requiresApiKey: true
  },
  perplexity: {
    providerId: 'perplexity',
    name: 'Perplexity',
    configType: 'apiKey',
    apiEndpoint: 'https://api.perplexity.ai',
    placeholder: 'pplx-...',
    requiresApiKey: true
  },
  qwen: {
    providerId: 'qwen',
    name: 'Alibaba Qwen',
    configType: 'apiKey',
    apiEndpoint: 'https://dashscope.aliyuncs.com/api/v1',
    placeholder: 'sk-...',
    requiresApiKey: true
  },
  together: {
    providerId: 'together',
    name: 'Together AI',
    configType: 'apiKey',
    apiEndpoint: 'https://api.together.xyz/v1',
    placeholder: 'api_key...',
    requiresApiKey: true
  },
  vertex: {
    providerId: 'vertex',
    name: 'Google Vertex AI',
    configType: 'apiKey',
    apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta',
    placeholder: 'AIza...',
    requiresApiKey: true
  },
  xai: {
    providerId: 'xai',
    name: 'xAI',
    configType: 'apiKey',
    apiEndpoint: 'https://api.x.ai/v1',
    placeholder: 'xai-...',
    requiresApiKey: true
  },
  ollama: {
    providerId: 'ollama',
    name: 'Ollama',
    configType: 'baseURL',
    placeholder: 'http://localhost:11434',
    defaultUrl: 'http://localhost:11434',
    requiresApiKey: false
  },
  lmstudio: {
    providerId: 'lmstudio',
    name: 'LM Studio',
    configType: 'baseURL',
    placeholder: 'http://localhost:1234',
    defaultUrl: 'http://localhost:1234',
    requiresApiKey: false
  }
};
